{% extends 'base.html' %}
{% load static %}

{% block title %}Accueil - Exam Store{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card text-center">
            <div class="card-header">
                <h1 class="card-title">
                    <i class="fas fa-star"></i> Bienvenue {{ name }}!
                </h1>
            </div>
            <div class="card-body">
                <p class="mb-3">Découvrez notre collection de produits exceptionnels</p>
                <a href="{% url 'products' %}" class="btn btn-primary">
                    <i class="fas fa-shopping-cart"></i> Voir nos produits
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-line"></i> Statistiques
                </h3>
            </div>
            <div class="card-body">
                <p><strong>Nombre magique:</strong> <span class="badge">{{ number }}</span></p>
                <p><strong>Éléments dans la liste:</strong> {{ mylist|length }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list"></i> Ma Liste
                </h3>
            </div>
            <div class="card-body">
                <p><strong>Contenu de la liste:</strong> {{ mylist }}</p>
                <ul class="list-styled">
                    {% for item in mylist %}
                        <li class="list-item">
                            <span class="item-number">{{ forloop.counter }}</span>
                            <span class="item-value">{{ item }}</span>
                            {% if item >= 3 %}
                                <span class="badge badge-success">
                                    <i class="fas fa-check"></i> ≥ 3
                                </span>
                            {% else %}
                                <span class="badge badge-warning">
                                    <i class="fas fa-exclamation"></i> < 3
                                </span>
                            {% endif %}
                        </li>
                    {% empty %}
                        <li class="text-center">
                            <i class="fas fa-inbox"></i> Aucun élément dans la liste
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-rocket"></i> Fonctionnalités
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-4 text-center">
                        <i class="fas fa-box fa-3x mb-2" style="color: var(--secondary-color);"></i>
                        <h4>Gestion des Produits</h4>
                        <p>Gérez facilement votre catalogue de produits</p>
                    </div>
                    <div class="col-4 text-center">
                        <i class="fas fa-users fa-3x mb-2" style="color: var(--success-color);"></i>
                        <h4>Interface Utilisateur</h4>
                        <p>Design moderne et responsive</p>
                    </div>
                    <div class="col-4 text-center">
                        <i class="fas fa-cog fa-3x mb-2" style="color: var(--warning-color);"></i>
                        <h4>Administration</h4>
                        <p>Panel d'administration Django intégré</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 0.375rem;
    color: white;
    background-color: var(--secondary-color);
}

.badge-success {
    background-color: var(--success-color);
}

.badge-warning {
    background-color: var(--warning-color);
}

.list-styled {
    list-style: none;
    padding: 0;
}

.list-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.list-item:hover {
    background-color: #d5dbdb;
    transform: translateX(5px);
}

.item-number {
    background-color: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.item-value {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--dark-color);
}
</style>
{% endblock %}
