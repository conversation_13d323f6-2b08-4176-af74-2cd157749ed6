#!/usr/bin/env python
"""Script pour créer des produits d'exemple"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Exam.settings')
django.setup()

from products.models import Products

def create_sample_products():
    """Créer des produits d'exemple"""
    
    # Supprimer les produits existants
    Products.objects.all().delete()
    
    # Créer des produits d'exemple
    products_data = [
        {
            'name': 'Smartphone Premium',
            'description': 'Un smartphone haut de gamme avec toutes les dernières fonctionnalités. Écran OLED, appareil photo professionnel, et batterie longue durée.',
            'price': 899.99,
            'active': True,
            'slug': 'smartphone-premium'
        },
        {
            'name': 'Ordinateur Portable Gaming',
            'description': 'Ordinateur portable conçu pour les gamers avec processeur haute performance, carte graphique dédiée et écran 144Hz.',
            'price': 1299.99,
            'active': True,
            'slug': 'ordinateur-portable-gaming'
        },
        {
            'name': 'Casque Audio Sans Fil',
            'description': 'Casque audio Bluetooth avec réduction de bruit active, autonomie 30h et qualité sonore exceptionnelle.',
            'price': 249.99,
            'active': True,
            'slug': 'casque-audio-sans-fil'
        },
        {
            'name': 'Montre Connectée',
            'description': 'Montre intelligente avec suivi de la santé, GPS intégré, résistante à l\'eau et écran tactile couleur.',
            'price': 399.99,
            'active': True,
            'slug': 'montre-connectee'
        },
        {
            'name': 'Tablette Graphique',
            'description': 'Tablette professionnelle pour les créatifs avec stylet de précision et surface de dessin large.',
            'price': 199.99,
            'active': True,
            'slug': 'tablette-graphique'
        },
        {
            'name': 'Enceinte Bluetooth',
            'description': 'Enceinte portable avec son 360°, étanche et autonomie de 12 heures. Parfaite pour vos sorties.',
            'price': 79.99,
            'active': True,
            'slug': 'enceinte-bluetooth'
        },
        {
            'name': 'Appareil Photo Numérique',
            'description': 'Appareil photo reflex pour photographes amateurs et professionnels avec objectif 18-55mm inclus.',
            'price': 649.99,
            'active': False,  # Produit indisponible
            'slug': 'appareil-photo-numerique'
        },
        {
            'name': 'Clavier Mécanique RGB',
            'description': 'Clavier gaming mécanique avec rétroéclairage RGB personnalisable et switches tactiles.',
            'price': 129.99,
            'active': True,
            'slug': 'clavier-mecanique-rgb'
        }
    ]
    
    created_products = []
    for product_data in products_data:
        product = Products.objects.create(**product_data)
        created_products.append(product)
        print(f"✓ Produit créé: {product.name} - {product.price}€")
    
    print(f"\n🎉 {len(created_products)} produits créés avec succès!")
    return created_products

if __name__ == '__main__':
    create_sample_products()
