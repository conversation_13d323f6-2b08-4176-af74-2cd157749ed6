{% extends 'base.html' %} {% load static %} {% block title %}Contact - Exam
Store{% endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header text-center">
        <h1 class="card-title">
          <i class="fas fa-envelope"></i> Contactez-nous
        </h1>
        <p>Nous sommes là pour vous aider</p>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-8">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-paper-plane"></i> Envoyez-nous un message
        </h3>
      </div>
      <div class="card-body">
        <form class="contact-form">
          <div class="form-group">
            <label for="name"> <i class="fas fa-user"></i> Nom complet </label>
            <input
              type="text"
              id="name"
              name="name"
              class="form-control"
              required />
          </div>

          <div class="form-group">
            <label for="email"> <i class="fas fa-envelope"></i> Email </label>
            <input
              type="email"
              id="email"
              name="email"
              class="form-control"
              required />
          </div>

          <div class="form-group">
            <label for="subject"> <i class="fas fa-tag"></i> Sujet </label>
            <select
              id="subject"
              name="subject"
              class="form-control">
              <option value="">Choisissez un sujet</option>
              <option value="info">Demande d'information</option>
              <option value="support">Support technique</option>
              <option value="commande">Question sur une commande</option>
              <option value="autre">Autre</option>
            </select>
          </div>

          <div class="form-group">
            <label for="message">
              <i class="fas fa-comment"></i> Message
            </label>
            <textarea
              id="message"
              name="message"
              class="form-control"
              rows="5"
              required></textarea>
          </div>

          <button
            type="submit"
            class="btn btn-primary">
            <i class="fas fa-paper-plane"></i> Envoyer le message
          </button>
        </form>
      </div>
    </div>
  </div>

  <div class="col-4">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-info-circle"></i> Informations de contact
        </h3>
      </div>
      <div class="card-body">
        <div class="contact-info">
          <div class="contact-item">
            <i class="fas fa-map-marker-alt"></i>
            <div>
              <strong>Adresse</strong>
              <p>123 Rue de l'Innovation<br />75001 Paris, France</p>
            </div>
          </div>

          <div class="contact-item">
            <i class="fas fa-phone"></i>
            <div>
              <strong>Téléphone</strong>
              <p>+33 1 23 45 67 89</p>
            </div>
          </div>

          <div class="contact-item">
            <i class="fas fa-envelope"></i>
            <div>
              <strong>Email</strong>
              <p><EMAIL></p>
            </div>
          </div>

          <div class="contact-item">
            <i class="fas fa-clock"></i>
            <div>
              <strong>Horaires</strong>
              <p>Lun - Ven: 9h00 - 18h00<br />Sam: 10h00 - 16h00</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card mt-3">
      <div class="card-header">
        <h3 class="card-title"><i class="fas fa-share-alt"></i> Suivez-nous</h3>
      </div>
      <div class="card-body text-center">
        <div class="social-links">
          <a
            href="#"
            class="social-link facebook">
            <i class="fab fa-facebook-f"></i>
          </a>
          <a
            href="#"
            class="social-link twitter">
            <i class="fab fa-twitter"></i>
          </a>
          <a
            href="#"
            class="social-link instagram">
            <i class="fab fa-instagram"></i>
          </a>
          <a
            href="#"
            class="social-link linkedin">
            <i class="fab fa-linkedin-in"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-question-circle"></i> Questions fréquentes
        </h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-6">
            <div class="faq-item">
              <h5>
                <i class="fas fa-shipping-fast"></i> Quels sont vos délais de
                livraison ?
              </h5>
              <p>
                Nous livrons généralement sous 2-3 jours ouvrés pour la France
                métropolitaine.
              </p>
            </div>

            <div class="faq-item">
              <h5>
                <i class="fas fa-undo"></i> Puis-je retourner un produit ?
              </h5>
              <p>
                Oui, vous avez 14 jours pour retourner un produit non utilisé.
              </p>
            </div>
          </div>

          <div class="col-6">
            <div class="faq-item">
              <h5>
                <i class="fas fa-credit-card"></i> Quels moyens de paiement
                acceptez-vous ?
              </h5>
              <p>
                Nous acceptons les cartes bancaires, PayPal et les virements.
              </p>
            </div>

            <div class="faq-item">
              <h5>
                <i class="fas fa-headset"></i> Comment contacter le support ?
              </h5>
              <p>
                Utilisez ce formulaire ou appelez-nous directement au numéro
                indiqué.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_css %}
<style>
  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-color);
  }

  .form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--light-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
  }

  .form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }

  .contact-item i {
    color: var(--secondary-color);
    font-size: 1.2rem;
    margin-top: 0.2rem;
    min-width: 20px;
  }

  .contact-item strong {
    color: var(--primary-color);
    display: block;
    margin-bottom: 0.25rem;
  }

  .contact-item p {
    margin: 0;
    color: var(--gray);
    line-height: 1.4;
  }

  .social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: var(--transition);
  }

  .social-link:hover {
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
  }

  .social-link.facebook {
    background-color: #3b5998;
  }
  .social-link.twitter {
    background-color: #1da1f2;
  }
  .social-link.instagram {
    background-color: #e4405f;
  }
  .social-link.linkedin {
    background-color: #0077b5;
  }

  .faq-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--light-color);
  }

  .faq-item:last-child {
    border-bottom: none;
  }

  .faq-item h5 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
  }

  .faq-item h5 i {
    color: var(--secondary-color);
    margin-right: 0.5rem;
  }

  .faq-item p {
    color: var(--gray);
    margin: 0;
    line-height: 1.5;
  }
</style>
{% endblock %} {% block extra_js %}
<script>
  document
    .querySelector(".contact-form")
    .addEventListener("submit", function (e) {
      e.preventDefault();
      alert(
        "Merci pour votre message ! Nous vous répondrons dans les plus brefs délais."
      );
    });
</script>
{% endblock %}
