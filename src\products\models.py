from django.db import models

class Products(models.Model):
    name = models.CharField(max_length=150)
    description = models.TextField(null=True, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    image=models.ImageField(upload_to='images/', null=True, blank=True)
    slug=models.SlugField(null=True)
    active=models.BooleanField(default=True)
    
    # class Meta():
    class Meta():
        verbose_name='Product'
        verbose_name_plural='Products'
        ordering =['name']
    def __str__(self):
        return self.name
        
# python manage.py makemigrations
# python manage.py migrate