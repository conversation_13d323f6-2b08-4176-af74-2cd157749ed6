{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Exam Project{% endblock %}</title>

    <!-- CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="header-content">
                <a href="{% url 'home' %}" class="logo">
                    <i class="fas fa-store"></i> Exam Store
                </a>

                <nav>
                    <ul>
                        <li><a href="{% url 'home' %}" {% if request.resolver_match.url_name == 'home' %}class="active"{% endif %}>
                            <i class="fas fa-home"></i> Accueil
                        </a></li>
                        <li><a href="{% url 'products' %}" {% if request.resolver_match.url_name == 'products' %}class="active"{% endif %}>
                            <i class="fas fa-box"></i> Produits
                        </a></li>
                        <li><a href="{% url 'about' %}" {% if request.resolver_match.url_name == 'about' %}class="active"{% endif %}>
                            <i class="fas fa-info-circle"></i> À propos
                        </a></li>
                        <li><a href="{% url 'contact' %}" {% if request.resolver_match.url_name == 'contact' %}class="active"{% endif %}>
                            <i class="fas fa-envelope"></i> Contact
                        </a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <div class="container">
            {% block content %}
            {% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; 2025 Exam Store. Tous droits réservés.</p>
            <p>Développé avec <i class="fas fa-heart" style="color: #e74c3c;"></i> par Ali Dialo</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Add fade-in animation to cards
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card, .product-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('fade-in');
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
