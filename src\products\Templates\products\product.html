{% extends 'base.html' %}
{% load static %}

{% block title %}Produits - Exam Store{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header text-center">
                <h1 class="card-title">
                    <i class="fas fa-box"></i> Nos Produits
                </h1>
                <p>Découvrez notre sélection de produits de qualité</p>
            </div>
        </div>
    </div>
</div>

{% if products %}
<div class="products-grid">
    {% for product in products %}
    <div class="product-card">
        {% if product.image %}
            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
        {% else %}
            <div class="product-image">
                <i class="fas fa-image fa-3x" style="color: var(--gray); margin-top: 80px;"></i>
            </div>
        {% endif %}
        
        <div class="product-info">
            <h3 class="product-title">{{ product.name }}</h3>
            <p class="product-price">${{ product.price }}</p>
            
            {% if product.description %}
                <p class="product-description">{{ product.description|truncatewords:20 }}</p>
            {% endif %}
            
            <div class="product-actions">
                <button class="btn btn-primary">
                    <i class="fas fa-shopping-cart"></i> Ajouter au panier
                </button>
                <button class="btn btn-success">
                    <i class="fas fa-eye"></i> Voir détails
                </button>
            </div>
            
            {% if not product.active %}
                <div class="product-status">
                    <span class="badge badge-warning">
                        <i class="fas fa-pause"></i> Indisponible
                    </span>
                </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-box-open fa-5x mb-3" style="color: var(--gray);"></i>
                <h3>Aucun produit disponible</h3>
                <p class="mb-3">Il n'y a actuellement aucun produit dans notre catalogue.</p>
                <a href="{% url 'home' %}" class="btn btn-primary">
                    <i class="fas fa-home"></i> Retour à l'accueil
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle"></i> Informations
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-4 text-center">
                        <i class="fas fa-truck fa-2x mb-2" style="color: var(--success-color);"></i>
                        <h5>Livraison Gratuite</h5>
                        <p>Pour toute commande supérieure à 50€</p>
                    </div>
                    <div class="col-4 text-center">
                        <i class="fas fa-shield-alt fa-2x mb-2" style="color: var(--secondary-color);"></i>
                        <h5>Garantie Qualité</h5>
                        <p>Tous nos produits sont garantis</p>
                    </div>
                    <div class="col-4 text-center">
                        <i class="fas fa-headset fa-2x mb-2" style="color: var(--warning-color);"></i>
                        <h5>Support 24/7</h5>
                        <p>Notre équipe est là pour vous aider</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.product-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.product-actions .btn {
    flex: 1;
    font-size: 0.9rem;
    padding: 0.5rem;
}

.product-status {
    margin-top: 1rem;
    text-align: center;
}

.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 0.375rem;
    color: white;
    background-color: var(--secondary-color);
}

.badge-warning {
    background-color: var(--warning-color);
}

@media (max-width: 768px) {
    .product-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
