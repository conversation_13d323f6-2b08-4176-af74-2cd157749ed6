from django.shortcuts import render, redirect
from django.http import HttpResponse
from .models import Products

def home(request):
    name = '<PERSON>'
    number = 10
    mylist = [1, 2, 4, 5, 6]
    return render(request, 'index.html', context={
        'name': name,
        'number': number,
        'mylist': mylist
    })

def contact(request):
    return render(request, 'contact.html')

def about(request):
    return render(request, 'about.html')

def products(request):
    products_list = Products.objects.filter(active=True).order_by('name')
    return render(request, 'products/product.html', context={
        'products': products_list
    })